import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sign_language/src/common/routes.dart';
import 'package:sign_language/src/presentation/page/Login/login_screen.dart';
import 'package:sign_language/src/presentation/page/auth/sign_in_screen.dart';
import 'package:sign_language/src/presentation/page/auth/sign_up_screen.dart';
import 'package:sign_language/src/presentation/page/dashboard/dashboard_screen.dart';
import 'package:sign_language/src/presentation/page/error/error_screen.dart';
import 'package:sign_language/src/presentation/page/forgot_password/forgot_password_screen.dart';
import 'package:sign_language/src/presentation/page/forgot_password/email_check_screen.dart';
import 'package:sign_language/src/presentation/page/forgot_password/verification_screen.dart';
import 'package:sign_language/src/presentation/page/forgot_password/change_password_screen.dart';
import 'package:sign_language/src/presentation/page/signup/signup_screen.dart';
import 'package:sign_language/src/presentation/page/translate/translate_screen.dart';
import 'package:sign_language/src/presentation/page/splash/splash_screen.dart';
import 'package:sign_language/src/presentation/page/onboard/onboarding_screen.dart';
import 'package:sign_language/src/presentation/page/onboard/avatar_selection_screen.dart';
import 'package:sign_language/src/presentation/page/profile/profile_screen.dart';
import 'package:sign_language/src/utilities/logger.dart';

GoRouter routerinit = GoRouter(
  routes: <RouteBase>[
    ///  =================================================================
    ///  ********************** Splash Route *****************************
    /// ==================================================================
    GoRoute(
      name: AppRoutes.SPLASH_ROUTE_NAME,
      path: AppRoutes.SPLASH_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const SplashScreen();
      },
    ),

    ///  =================================================================
    /// ********************** Authentication Routes ********************
    /// ==================================================================
    GoRoute(
      name: AppRoutes.ONBOARDING_ROUTE_NAME,
      path: AppRoutes.ONBOARDING_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const OnboardingScreen();
      },
    ),
    GoRoute(
      name: AppRoutes.LOGIN_ROUTE_NAME,
      path: AppRoutes.LOGIN_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const LoginScreen();
      },
    ),
    GoRoute(
      name: AppRoutes.SIGNUP_ROUTE_NAME,
      path: AppRoutes.SIGNUP_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const SignupScreen();
      },
    ),
    GoRoute(
      name: AppRoutes.FORGOT_PASSWORD_ROUTE_NAME,
      path: AppRoutes.FORGOT_PASSWORD_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const ForgotPasswordScreen();
      },
    ),
    GoRoute(
      name: AppRoutes.EMAIL_CHECK_ROUTE_NAME,
      path: AppRoutes.EMAIL_CHECK_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        final email = state.uri.queryParameters['email'] ?? '';
        return EmailCheckScreen(email: email);
      },
    ),
    GoRoute(
      name: AppRoutes.VERIFICATION_ROUTE_NAME,
      path: AppRoutes.VERIFICATION_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        final email = state.uri.queryParameters['email'] ?? '';
        return VerificationScreen(email: email);
      },
    ),
    GoRoute(
      name: AppRoutes.CHANGE_PASSWORD_ROUTE_NAME,
      path: AppRoutes.CHANGE_PASSWORD_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        final email = state.uri.queryParameters['email'] ?? '';
        return ChangePasswordScreen(email: email);
      },
    ),
    GoRoute(
      name: AppRoutes.TRANSLATE_ROUTE_NAME,
      path: AppRoutes.TRANSLATE_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const TranslateScreen();
      },
    ),
    GoRoute(
      name: AppRoutes.AVATAR_SELECTION_ROUTE_NAME,
      path: AppRoutes.AVATAR_SELECTION_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const AvatarSelectionScreen();
      },
    ),
    GoRoute(
      name: AppRoutes.PROFILE_ROUTE_NAME,
      path: AppRoutes.PROFILE_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const ProfileScreen();
      },
    ),

    ///  =================================================================
    /// ********************** DashBoard Route ******************************
    /// ==================================================================
    GoRoute(
      name: AppRoutes.DASHBOARD_ROUTE_NAME,
      path: AppRoutes.DASHBOARD_ROUTE_PATH,
      builder: (BuildContext context, GoRouterState state) {
        return const DashBoardScreen();
      },
    ),
  ],
  errorPageBuilder: (context, state) {
    return const MaterialPage(child: ErrorScreen());
  },
  redirect: (context, state) {
    logger.info('redirect: ${state.uri}');
    return null;
  },
);
