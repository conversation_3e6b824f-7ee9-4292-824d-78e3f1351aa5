import 'package:flutter/material.dart';

class ProfilePopupButton extends StatefulWidget {
  const ProfilePopupButton({super.key});

  @override
  State<ProfilePopupButton> createState() => _ProfilePopupButtonState();
}

class _ProfilePopupButtonState extends State<ProfilePopupButton> {
  OverlayEntry? _overlayEntry;

  void _showPopupMenu(BuildContext context) {
    final overlay = Overlay.of(context);
    final renderBox = context.findRenderObject() as RenderBox;
    final offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: offset.dy + 50,
        right: 16,
        child: Material(
          color: Colors.transparent,
          child: Container(
            width: 220,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: const LinearGradient(
                colors: [Colors.white, Color(0xFFDDDDFD)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 12,
                )
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Align(
                  alignment: Alignment.topRight,
                  child: Container(
                    color: Colors.black,
                    child: InkWell(
                      onTap: () => _overlayEntry?.remove(),
                      child: const Icon(Icons.close, size: 20),
                    ),
                  ),
                ),
                _buildRow(Icons.person, 'Account'),
                _buildRow(Icons.edit, 'Change Avatar'),
                const Divider(),
                _buildRow(Icons.settings, 'Settings'),
                _buildRow(Icons.help_outline, 'Help & Support'),
                _buildRow(Icons.security, 'Privacy & Security'),
                const SizedBox(height: 10),
              ],
            ),
          ),
        ),
      ),
    );

    overlay.insert(_overlayEntry!);
  }

  Widget _buildRow(IconData icon, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.black87),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(fontSize: 14, color: Colors.black87),
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.more_vert),
      onPressed: () => _showPopupMenu(context),
    );
  }
}
