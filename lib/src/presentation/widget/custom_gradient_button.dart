import 'package:flutter/material.dart';
import 'package:sign_language/src/common/colors.dart';

class CustomGradientButton extends StatelessWidget {
  final VoidCallback onPressed;
  final double height;
  final List<Color> gradientColors;
  final AlignmentGeometry beginAt;
  final AlignmentGeometry endAt;
  final BorderRadiusGeometry borderRadius;
  final String label;
  const CustomGradientButton({
    super.key,
    required this.onPressed,
    this.height = 45,
    this.gradientColors = const [
      ColorLight.purpleMimosa,
      ColorLight.lightRoyalBlue,
    ],
    this.beginAt = Alignment.topCenter,
    this.endAt = Alignment.bottomCenter,
    this.borderRadius = const BorderRadius.all(Radius.circular(30)),
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.all(0),
        side: BorderSide.none,
      ),
      child: Container(
        width: double.infinity,
        height: height,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradientColors,
            begin: beginAt,
            end: endAt,
          ),
          borderRadius: borderRadius,
        ),
        child: Center(
          child: Text(
            label,
            style: Theme.of(context).textTheme.displayMedium?.copyWith(),
          ),
        ),
      ),
    );
  }
}
