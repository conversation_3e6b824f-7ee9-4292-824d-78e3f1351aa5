import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:sign_language/src/common/colors.dart';
import 'package:sign_language/src/common/constant.dart';
import 'package:sign_language/src/common/images.dart';

class CustomTransparentButton extends StatelessWidget {
  final VoidCallback onTap;
  final String? label;
  final bool isWithIcon;
  final String? icon;

  const CustomTransparentButton({
    super.key,
    required this.onTap,
    this.label,
    this.isWithIcon = true,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      onPressed: onTap,
      style: OutlinedButton.styleFrom(
        padding: EdgeInsets.zero,
        minimumSize: Size.zero, // Avoid enforcing min size
        tapTargetSize: MaterialTapTargetSize.shrinkWrap, // Remove extra space
        side: const BorderSide(color: ColorLight.card, width: 0.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min, // Only take required space
          children: [
            Text(
              label ?? 'continue_with'.tr(),
              style: Theme.of(context).textTheme.headlineMedium,
              overflow: TextOverflow.ellipsis, // Prevent overflow
            ),
            if (isWithIcon) ...[
              const SizedBox(width: 8),
              Image.asset(
                icon ?? Images.IC_Google,
                width: 20,
                height: 20,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
