import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sign_language/src/common/constant.dart';
import 'package:sign_language/src/common/images.dart';
import 'package:sign_language/src/presentation/bloc/bloc/language_bloc.dart';
import 'package:sign_language/src/presentation/bloc/bloc/language_event.dart';
import 'package:sign_language/src/presentation/bloc/bloc/language_state.dart';
import 'package:sign_language/src/presentation/widget/custom_gradient_button.dart';
import 'package:sign_language/src/presentation/widget/popup_widget.dart';
import 'signup_email_check_screen.dart';

class SignupEmailScreen extends StatefulWidget {
  const SignupEmailScreen({super.key});

  @override
  State<SignupEmailScreen> createState() => _SignupEmailScreenState();
}

class _SignupEmailScreenState extends State<SignupEmailScreen> {
  final TextEditingController _emailController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          // Background image
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(Images.APP_BG),
                fit: BoxFit.cover,
              ),
            ),
          ),

          // Foreground content with scroll
          SafeArea(
            child: SingleChildScrollView(
              reverse: true,
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom + 24,
                ),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 75),
                      Center(
                        child: Image.asset(
                          Images.APP_ICON,
                          width: 80,
                          height: 75,
                        ),
                      ),
                      const SizedBox(height: SPACE25),
                      Text(
                        'create_account'.tr(),
                        style: Theme.of(context).textTheme.displayLarge,
                      ),
                      const SizedBox(height: SPACE15),
                      Text(
                        'enter_email_to_create_account'.tr(),
                        style: Theme.of(context).textTheme.headlineMedium,
                      ),
                      const SizedBox(height: SPACE25),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: TextFormField(
                          controller: _emailController,
                          keyboardType: TextInputType.emailAddress,
                          style: const TextStyle(color: Colors.black),
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            hintText: 'email_hint'.tr(),
                            hintStyle: TextStyle(
                              color: Colors.grey[600],
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 16,
                            ),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'please_enter_email'.tr();
                            }
                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                              return 'please_enter_valid_email'.tr();
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: SPACE25),
                      CustomGradientButton(
                        onPressed: () {
                          if (_formKey.currentState!.validate()) {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => SignupEmailCheckScreen(
                                  email: _emailController.text.trim(),
                                ),
                              ),
                            );
                          }
                        },
                        label: 'continue'.tr(),
                        height: 56,
                      ),
                      const SizedBox(height: SPACE25),
                      Center(
                        child: TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: Text(
                            'back_to_login'.tr(),
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 14,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          
          // Language toggle button
          Positioned(
            right: 0,
            top: 50,
            child: Row(
              children: [
                BlocBuilder<LanguageBloc, LanguageState>(
                  builder: (context, state) {
                    context.setLocale(state.locale);
                    return TextButton(
                      onPressed: () {
                        context.read<LanguageBloc>().add(ToggleLanguage());
                      },
                      child: Text(
                        state.locale.languageCode.toUpperCase(),
                        style: const TextStyle(color: Colors.white),
                      ),
                    );
                  },
                ),
                ProfilePopupButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
