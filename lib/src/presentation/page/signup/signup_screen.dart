import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sign_language/src/common/constant.dart';
import 'package:sign_language/src/common/images.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:sign_language/src/presentation/widget/custom_gradient_button.dart';
import 'dart:ui' as ui;

class SignupScreen extends StatefulWidget {
  final String? email;

  const SignupScreen({
    super.key,
    this.email,
  });

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  final List<TextEditingController> _otpControllers = List.generate(4, (index) => TextEditingController());
  final List<FocusNode> _otpFocusNodes = List.generate(4, (index) => FocusNode());

  @override
  void dispose() {
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _otpFocusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  void _onOtpChanged(String value, int index) {
    if (value.isNotEmpty && index < 3) {
      _otpFocusNodes[index + 1].requestFocus();
    } else if (value.isEmpty && index > 0) {
      _otpFocusNodes[index - 1].requestFocus();
    }
  }

  void _createAccount() {
    // Get OTP code
    String otpCode = _otpControllers.map((controller) => controller.text).join();

    if (otpCode.length != 4) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter the complete verification code'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // TODO: Implement full account creation logic
    // - Validate all form fields
    // - Verify OTP code
    // - Create account with API
    // - Navigate to success screen or main app

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Account created successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(Images.APP_BG),
                fit: BoxFit.cover,
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              reverse: true,
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom + 24,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 75),
                    Center(
                      child: Image.asset(
                        Images.APP_ICON,
                        width: 80,
                        height: 75,
                      ),
                    ),
                    const SizedBox(height: SPACE25),
                    Text(
                      'Sign up',
                      style: Theme.of(context).textTheme.displayLarge,
                    ),
                    const SizedBox(height: SPACE15),
                    Text(
                      'Enter your name',
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    const SizedBox(height: SPACE12),
                    _buildTextField(),
                    const SizedBox(height: SPACE15),
                    Text(
                      'Enter your Password',
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    const SizedBox(height: SPACE12),
                    _buildTextField(obscure: true),
                    const SizedBox(height: SPACE15),
                    Text(
                      'Select Your Nationality',
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    const SizedBox(height: SPACE12),
                    _buildNationalityField(),
                    const SizedBox(height: SPACE15),
                    Text(
                      'Enter your Phone Number',
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    const SizedBox(height: SPACE12),
                    _buildMobTextField(),
                    const SizedBox(height: SPACE15),
                    Text(
                      'Enter Verification Code',
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    const SizedBox(height: SPACE12),
                    _buildOtpField(),
                    const SizedBox(height: SPACE25),
                    CustomGradientButton(
                      onPressed: () {
                        // TODO: Implement account creation logic
                        // Validate all fields and create account
                        _createAccount();
                      },
                      label: 'Create Account',
                      height: 56,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({bool obscure = false}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextField(
        style: const TextStyle(
          color: Colors.black,
          fontSize: 16,
        ),
        obscureText: obscure,
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
      ),
    );
  }

  Widget _buildNationalityField({bool obscure = false}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextField(
        style: const TextStyle(
          color: Colors.black,
          fontSize: 16,
        ),
        obscureText: obscure,
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
      ),
    );
  }

  Widget _buildMobTextField({bool obscure = false}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextFormField(
        style: const TextStyle(
          color: Colors.black,
          fontSize: 16,
        ),
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          prefixIcon: CountryCodePicker(
            showDropDownButton: true,
            textStyle: const TextStyle(
              color: Colors.black,
              fontSize: 16,
            ),
            initialSelection: "sa",
            onChanged: (value) {
              print("value => $value");
            },
            searchDecoration: const InputDecoration(label: Text("Enter Code")),
          ),
        ),
      ),
    );
  }

  Widget _buildOtpField() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(4, (index) => _buildOtpPinField(index)),
      ),
    );
  }

  Widget _buildOtpPinField(int index) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _otpFocusNodes[index].hasFocus ? const Color(0xFF6366F1) : Colors.grey[300]!,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _otpControllers[index],
        focusNode: _otpFocusNodes[index],
        textAlign: TextAlign.center,
        textDirection: ui.TextDirection.ltr,
        keyboardType: TextInputType.number,
        maxLength: 1,
        style: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: Color(0xFF1A1A1A),
        ),
        decoration: const InputDecoration(
          border: InputBorder.none,
          counterText: '',
          contentPadding: EdgeInsets.zero,
        ),
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        onChanged: (value) => _onOtpChanged(value, index),
      ),
    );
  }
}
