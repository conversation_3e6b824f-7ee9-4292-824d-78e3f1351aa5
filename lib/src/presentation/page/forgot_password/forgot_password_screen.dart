import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'package:go_router/go_router.dart';
import 'package:sign_language/src/common/constant.dart';
import 'package:sign_language/src/common/images.dart';
import 'package:sign_language/src/common/routes.dart';
import 'package:sign_language/src/presentation/widget/custom_gradient_button.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final TextEditingController _emailController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          // Background image
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(Images.APP_BG),
                fit: BoxFit.cover,
              ),
            ),
          ),

          // Foreground content with scroll
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom + 24,
                ),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 20),

                      // Back button
                      IconButton(
                        onPressed: () => context.pop(),
                        icon: const Icon(
                          Icons.arrow_back_ios,
                          color: Colors.white,
                          size: 24,
                        ),
                        padding: EdgeInsets.zero,
                        alignment: Alignment.centerLeft,
                      ),

                      const SizedBox(height: 60),

                      // App icon
                      Center(
                        child: Image.asset(
                          Images.APP_ICON,
                          width: 80,
                          height: 75,
                        ),
                      ),

                      const SizedBox(height: 150),
                      
                      // Title
                      Center(
                        child: Text(
                          'reset_password'.tr(),
                          style: Theme.of(context).textTheme.displayLarge?.copyWith(
                            fontSize: 28,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Description
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Text(
                            'reset_password_description'.tr(),
                            textAlign: TextAlign.center,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withValues(alpha: 0.8),
                              fontSize: 16,
                              height: 1.4,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 40),
                      
                      // Email label
                      Padding(
                        padding: const EdgeInsets.only(left: 4),
                        child: Text(
                          'enter_email'.tr(),
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),

                      const SizedBox(height: 12),

                      // Email input field
                      _buildEmailTextField(),

                      const SizedBox(height: 40),

                      // Send reset link button
                      CustomGradientButton(
                        onPressed: _sendResetLink,
                        label: 'send'.tr(),
                        height: 56,
                      ),
                      
                      const SizedBox(height: SPACE15),
                      
                      // Back to login
                      Center(
                        child: TextButton(
                          onPressed: () => context.pop(),
                          child: Text(
                            'back_to_login'.tr(),
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 14,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmailTextField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _emailController,
        keyboardType: TextInputType.emailAddress,
        textDirection: ui.TextDirection.ltr,
        textAlign: TextAlign.left,
        style: const TextStyle(
          color: Colors.black87,
          fontSize: 16,
        ),
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
          hintText: 'enter_email_hint'.tr(),
          hintStyle: const TextStyle(
            color: Colors.black54,
            fontSize: 16,
          ),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'email_required'.tr();
          }
          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
            return 'email_invalid'.tr();
          }
          return null;
        },
      ),
    );
  }

  void _sendResetLink() {
    // if (_formKey.currentState!.validate()) {
      // TODO: Implement forgot password logic here
      // Navigate to email check screen
      context.push(
        '${AppRoutes.EMAIL_CHECK_ROUTE_PATH}?email=${Uri.encodeComponent(_emailController.text)}',
      );
    // }
  }
}
