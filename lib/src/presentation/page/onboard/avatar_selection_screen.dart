import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sign_language/src/common/routes.dart';
import 'package:sign_language/src/common/images.dart';
import '../../widget/custom_gradient_button.dart';
import 'package:carousel_slider/carousel_slider.dart';


class AvatarSelectionScreen extends StatefulWidget {
  const AvatarSelectionScreen({super.key});

  @override
  State<AvatarSelectionScreen> createState() => _AvatarSelectionScreenState();
}

class _AvatarSelectionScreenState extends State<AvatarSelectionScreen> {
  int selectedAvatarIndex = 0;
  
  final List<AvatarData> avatars = [
    AvatarData(
      name: '<PERSON>',
      imagePath: 'assets/images/avatar_1.png',
    ),
    AvatarData(
      name: 'Fatima',
      imagePath: 'assets/images/avatar_2.png',
    ),
    AvatarData(
      name: 'Saud',
      imagePath: 'assets/images/avatar_3.png',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Images.APP_BG),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              const SizedBox(height: 60),
              
              // Title
              const Text(
                'Select\nYour Avatar',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  height: 1.2,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 80),
              
              // Avatar Carousel
              Expanded(
                child: CarouselSlider.builder(
                  itemCount: avatars.length,
                  itemBuilder: (context, index, realIndex) {
                    return _buildCascadedAvatarCard(avatars[index], index == selectedAvatarIndex, index);
                  },
                  options: CarouselOptions(
                    height: 300,
                    viewportFraction: 0.5,
                    enlargeCenterPage: true,
                    enlargeFactor: 0.2,
                    enableInfiniteScroll: true,
                    autoPlay: false,
                    onPageChanged: (index, reason) {
                      setState(() {
                        selectedAvatarIndex = index;
                      });
                    },
                  ),
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Page Indicators
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  avatars.length,
                  (index) => Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: index == selectedAvatarIndex ? 12 : 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: index == selectedAvatarIndex
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.4),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: 60),
              
              // Continue Button
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: CustomGradientButton(
                  onPressed: () => _onContinue(context),
                  label: 'Continue',
                  height: 56,
                ),
              ),
              
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCascadedAvatarCard(AvatarData avatar, bool isSelected, int index) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Cascaded Square Avatar Stack
        Stack(
          alignment: Alignment.center,
          children: [
            // Background squares for cascaded effect with closer spacing
            Opacity(
              opacity: isSelected ? 1.0 : 0.4,
              child: Container(
                width: 200,
                height: 200,
            
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: Image.asset(
                    avatar.imagePath,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        decoration: BoxDecoration(
                          color: Colors.white24,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Icon(
                          Icons.person,
                          size: 80,
                          color: Colors.white70,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),

            // Checkmark for selected avatar
            if (isSelected)
              Positioned(
                top: 10,
                right: 10,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: const BoxDecoration(
                    color: Color(0xFF00C851),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 8,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              ),
          ],
        ),

        const SizedBox(height: 30),

        // Avatar Name with enhanced styling
        SizedBox(
          width: 200,
          child: Text(
            avatar.name,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            style: TextStyle(
              fontSize: isSelected ? 20 : 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
              shadows: isSelected ? [
                const Shadow(
                  color: Colors.black26,
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ] : null,
            ),
          ),
        ),
      ],
    );
  }

  void _onContinue(BuildContext context) {
    // Navigate to profile screen
    context.pushNamed(AppRoutes.PROFILE_ROUTE_NAME);
  }
}

class AvatarData {
  final String name;
  final String imagePath;

  AvatarData({
    required this.name,
    required this.imagePath,
  });
}
