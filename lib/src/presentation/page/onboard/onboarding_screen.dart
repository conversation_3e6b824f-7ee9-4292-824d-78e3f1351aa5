import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:introduction_screen/introduction_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:sign_language/src/common/images.dart';
import 'avatar_selection_screen.dart';

class OnboardingScreen extends StatelessWidget {
  const OnboardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;
    final isTablet = screenWidth > 600;
    final isLargeScreen = screenWidth > 800;

    // Responsive values
    final titleTopPosition = screenHeight * 0.12; // 12% from top
    final titleFontSize = isLargeScreen ? 28.0 : isTablet ? 25.0 : 22.0;
    final containerHeight = screenHeight * 0.40; // 45% of screen height
    final introTopPosition = screenHeight * 0.22; // 20% from top
    final buttonPadding = EdgeInsets.symmetric(
      horizontal: screenWidth * 0.03, // 3% of screen width
      vertical: isTablet ? 8 : 6,
    );
    final buttonFontSize = isLargeScreen ? 14.0 : isTablet ? 13.0 : 12.0;

    // Set status bar to transparent
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
      ),
    );

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Images.APP_BG),
            fit: BoxFit.cover,
          ),
        ),
        child: Stack(
          children: [
                  Positioned(
                    top: titleTopPosition,
                    left: 0,
                    right: 0,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.05),
                      child: Text(
                        'Welcome to\nSaudi Sign Language',
                        style: TextStyle(
                          fontSize: titleFontSize,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: containerHeight,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(isTablet ? 40 : 30),
                      topRight: Radius.circular(isTablet ? 40 : 30)
                    ),
                    color: const Color.fromARGB(255, 23, 29, 120),
                  ),
                ),
              ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              top: introTopPosition,
              child: IntroductionScreen(
                pages: _buildPages(context, screenWidth, screenHeight, isTablet, isLargeScreen),
                onDone: () => _onIntroEnd(context),
                onSkip: () => _onIntroEnd(context),
                showSkipButton: true,
                skipOrBackFlex: 1,
                nextFlex: 1,
                showBackButton: true,
                globalBackgroundColor: Colors.transparent,
                back: Container(
                  padding: buttonPadding,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(isTablet ? 25 : 20),
                  ),
                  child: Text(
                    'Previous',
                    style: TextStyle(color: Colors.white, fontSize: buttonFontSize),
                  ),
                ),
                skip: Container(
                  padding: buttonPadding,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(isTablet ? 25 : 20),
                  ),
                  child: Text(
                    'skip'.tr(),
                    style: TextStyle(color: Colors.white, fontSize: buttonFontSize),
                  ),
                ),
                next: Container(
                  padding: buttonPadding,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(isTablet ? 25 : 20),
                  ),
                  child: Text(
                    'Next',
                    style: TextStyle(color: const Color(0xFF1A1B3A), fontSize: buttonFontSize, fontWeight: FontWeight.w600),
                  ),
                ),
                done: Container(
                  padding: buttonPadding,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(isTablet ? 25 : 20),
                  ),
                  child: Text(
                    'done'.tr(),
                    style: TextStyle(color: const Color(0xFF1A1B3A), fontSize: buttonFontSize, fontWeight: FontWeight.w600),
                  ),
                ),
                curve: Curves.fastLinearToSlowEaseIn,
                controlsMargin: EdgeInsets.symmetric(
                  horizontal: screenWidth * 0.04, // 4% of screen width
                  vertical: isTablet ? 32 : 24,
                ),
                controlsPadding: EdgeInsets.symmetric(
                  horizontal: screenWidth * 0.01, // 1% of screen width
                  vertical: 4.0,
                ),
                dotsDecorator: DotsDecorator(
                  size: Size(isTablet ? 8.0 : 6.0, isTablet ? 8.0 : 6.0),
                  color: Colors.white,
                  activeSize: Size(isTablet ? 28.0 : 20.0, isTablet ? 10.0 : 8.0),
                  activeColor: Colors.white,
                  activeShape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(25.0)),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<PageViewModel> _buildPages(BuildContext context, double screenWidth, double screenHeight, bool isTablet, bool isLargeScreen) {
    return [
      // Page 1: About the app
      PageViewModel(
        
        useScrollView: false,
        title: '',
        image: _buildImageWidget('assets/images/onboard_1.png', screenWidth, isTablet),
        bodyWidget: _buildBodyContent(
          'About the app',
          'The app translates the speech or text into signs performed by an avatar.',
          screenWidth,
          isTablet,
          isLargeScreen,
        ),
        decoration: _getPageDecoration(),
      ),
      // Page 2: Functions of the app
      PageViewModel(
        useScrollView: false,
         title: '',
         
        image: _buildImageWidget('assets/images/onboard_2.png', screenWidth, isTablet),
        bodyWidget: _buildBodyContent(
          'Functions of the app',
          'The app connects the user with sign language functions such as sign to sign, speech to sign, and signs dictionary.',
          screenWidth,
          isTablet,
          isLargeScreen,
        ),
        decoration: _getPageDecoration(),
      ),
      // Page 3: Control Buttons
      PageViewModel(
        useScrollView: false,
        
        title: '',
        image: _buildImageWidget('assets/images/onboard_3.png', screenWidth, isTablet),
        bodyWidget: _buildBodyContent(
          'Control Buttons',
          'The app allows the user to control the avatar performance by zooming in/out on the avatar speed/voice performing the signs.',
          screenWidth,
          isTablet,
          isLargeScreen,
        ),
        decoration: _getPageDecoration(),
      ),
      // Page 4: Different personalities
      PageViewModel(
        useScrollView: false,
        title: '',
        image: _buildImageWidget('assets/images/onboard_4.png', screenWidth, isTablet),
        bodyWidget: _buildBodyContent(
          'Different personalities',
          'The app allows the user to select the avatar personality that he prefers.',
          screenWidth,
          isTablet,
          isLargeScreen,
        ),
        decoration: _getPageDecoration(),
      ),
      // Page 5: Notice
      PageViewModel(
        useScrollView: false,
        title: '',
        image: _buildImageWidget('assets/images/onboard_5.png', screenWidth, isTablet),
        bodyWidget: _buildBodyContent(
          'Notice',
          'For the app to work it needs the mobile to be connected to the internet.',
          screenWidth,
          isTablet,
          isLargeScreen,
        ),
        decoration: _getPageDecoration(),
      ),
    ];
  }

  Widget _buildImageWidget(String assetName, double screenWidth, bool isTablet) {
    final imageSize = isTablet ? screenWidth * 0.4 : screenWidth * 0.75;

    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(isTablet ? 25 : 20),
          child: Image.asset(
            assetName,
            width: imageSize,
            height: imageSize,
            fit: BoxFit.cover,
          ),
        ),
      ],
    );
  }

  Widget _buildBodyContent(String subtitle, String description, double screenWidth, bool isTablet, bool isLargeScreen) {
    return Column(
      children: [
       
        Padding(
          padding: EdgeInsets.symmetric(horizontal: screenWidth * 0.06),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: isLargeScreen ? 22.0 : isTablet ? 20.0 : 18.0,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              SizedBox(height: isTablet ? 16 : 12),
              Text(
                description,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: isLargeScreen ? 16.0 : isTablet ? 15.0 : 13.0,
                  color: Colors.white,
                  height: 1.4,
                  
                ),
              ),
              // const SizedBox(height: 40), // Space for navigation buttons
            ],
          ),
        ),
      ],
    );
  }

  PageDecoration _getPageDecoration() {
    return const PageDecoration(
      bodyTextStyle: TextStyle(
        fontSize: 16.0,
        color: Colors.white,
        height: 1.5,
      ),
      bodyPadding: EdgeInsets.zero, // Remove padding to allow full width
      pageColor: Colors.transparent, // Transparent to show background image
      titlePadding: EdgeInsets.zero,
    // titleTextStyle: TextStyle(color: Color.fromARGB(255, 255, 255, 255),fontSize: 30),
      bodyAlignment: Alignment.center,
       imageFlex: 0,bodyFlex: 0,
      imageAlignment: Alignment.center,
    );
  }

  void _onIntroEnd(BuildContext context) {
    // Navigate to avatar selection screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AvatarSelectionScreen(),
      ),
    );
  }
}
