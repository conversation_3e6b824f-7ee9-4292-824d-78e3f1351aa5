import 'package:flutter/material.dart';

class DashBoardScreen extends StatefulWidget {
  const DashBoardScreen({super.key});

  @override
  State<DashBoardScreen> createState() => _DashBoardScreenState();
}

class _DashBoardScreenState extends State<DashBoardScreen> {
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: WaveBackground(),
    );
  }
}

class WaveBackground extends StatelessWidget {
  const WaveBackground({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Container(
      height: size.height,
      width: size.width,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple, Colors.blue],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Stack(
        children: [
          // Green curved shape at top left corner
          Positioned(
            top: 0,
            left: 0,
            child: <PERSON>Paint(
              size: <PERSON><PERSON>(size.width, size.height * 0.5),
              painter: <PERSON><PERSON><PERSON><PERSON>ain<PERSON>(),
            ),
          ),
        ],
      ),
    );
  }
}

class GreenCurvePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Create the curved path
    Path path = Path();
    path.moveTo(0, 0);
    path.lineTo(size.width * 0.7, 0);

    // Create smooth curves using cubicTo
    path.cubicTo(
      size.width * 0.8,
      size.height * 0.1,
      size.width * 0.9,
      size.height * 0.2,
      size.width * 0.8,
      size.height * 0.35,
    );

    path.cubicTo(
      size.width * 0.7,
      size.height * 0.5,
      size.width * 0.5,
      size.height * 0.6,
      size.width * 0.3,
      size.height * 0.7,
    );

    path.cubicTo(
      size.width * 0.2,
      size.height * 0.75,
      size.width * 0.1,
      size.height * 0.8,
      0,
      size.height * 0.9,
    );

    path.lineTo(0, 0);
    path.close();

    // Create green gradient paint
    Paint paint = Paint()
      ..style = PaintingStyle.fill
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Colors.green.shade300.withOpacity(0.8),
          Colors.green.shade500.withOpacity(0.7),
          Colors.green.shade700.withOpacity(0.6),
        ],
        stops: const [0.0, 0.5, 1.0],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class PurpleWavePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final path = Path();

    // Start from top-left corner
    path.moveTo(0, 0);

    // Draw vertical line down a bit
    path.lineTo(0, size.height * 0.5);

    // Inward S-curve (never crosses the center)
    path.cubicTo(
      size.width * 0.4,
      size.height * 0.25,
      size.width * 0.25,
      size.height * 0.35,
      size.width * 0.45,
      size.height - 480,
    );

    // Curve ends and joins top
    path.lineTo(size.width * 0.5, 0);

    // Close path back to top-left
    path.close();

    final paint = Paint()
      ..style = PaintingStyle.fill
      ..shader = const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFF3A0CA3),
          Color(0xFF4A0DA5),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// ------------------ Each CustomPainter Below ------------------ //

class WaveLayer1 extends StatelessWidget {
  const WaveLayer1({super.key});
  @override
  Widget build(BuildContext context) {
    return CustomPaint(painter: _Wave1Painter());
  }
}

class _Wave1Painter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..shader = LinearGradient(
        colors: [Color(0xFF130CB7), Color(0xFF7366FF)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    final path = Path();
    path.moveTo(0, size.height * 0.2);
    path.quadraticBezierTo(
        size.width * 0.3, size.height * 0.0, size.width, size.height * 0.2);
    path.lineTo(size.width, 0);
    path.lineTo(0, 0);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class WaveLayer2 extends StatelessWidget {
  const WaveLayer2({super.key});
  @override
  Widget build(BuildContext context) {
    return CustomPaint(painter: _Wave2Painter());
  }
}

class _Wave2Painter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.deepPurple.withOpacity(0.2)
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(0, size.height * 0.25);
    path.quadraticBezierTo(
        size.width * 0.4, size.height * 0.15, size.width, size.height * 0.3);
    path.lineTo(size.width, 0);
    path.lineTo(0, 0);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class WaveLayer3 extends StatelessWidget {
  const WaveLayer3({super.key});
  @override
  Widget build(BuildContext context) {
    return CustomPaint(painter: _Wave3Painter());
  }
}

class _Wave3Painter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.indigo.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(0, size.height * 0.4);
    path.quadraticBezierTo(
        size.width * 0.3, size.height * 0.35, size.width, size.height * 0.5);
    path.lineTo(size.width, 0);
    path.lineTo(0, 0);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class WaveLayer4 extends StatelessWidget {
  const WaveLayer4({super.key});
  @override
  Widget build(BuildContext context) {
    return CustomPaint(painter: _Wave4Painter());
  }
}

class _Wave4Painter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue.shade800.withOpacity(0.4)
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(0, size.height * 0.55);
    path.quadraticBezierTo(
        size.width * 0.5, size.height * 0.6, size.width, size.height * 0.58);
    path.lineTo(size.width, 0);
    path.lineTo(0, 0);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class WaveLayer5 extends StatelessWidget {
  const WaveLayer5({super.key});
  @override
  Widget build(BuildContext context) {
    return CustomPaint(painter: _Wave5Painter());
  }
}

class _Wave5Painter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.purple.shade800.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(0, size.height * 0.7);
    path.quadraticBezierTo(
        size.width * 0.2, size.height * 0.75, size.width, size.height * 0.68);
    path.lineTo(size.width, 0);
    path.lineTo(0, 0);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class WaveLayer6 extends StatelessWidget {
  const WaveLayer6({super.key});
  @override
  Widget build(BuildContext context) {
    return CustomPaint(painter: _Wave6Painter());
  }
}

class _Wave6Painter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..shader = LinearGradient(
        colors: [Color(0xFF7F00FF), Color(0xFFE100FF)],
        begin: Alignment.bottomLeft,
        end: Alignment.topRight,
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    final path = Path();
    path.moveTo(0, size.height * 0.85);
    path.quadraticBezierTo(
        size.width * 0.5, size.height * 0.95, size.width, size.height * 0.8);
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
