import 'package:flutter/material.dart';
import '../../../common/images.dart';

class SubscriptionScreen extends StatelessWidget {
  const SubscriptionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Images.APP_BG),
            fit: BoxFit.cover,
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Header with back button and menu
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Image.asset(
                      'assets/images/back.png',
                      width: 24,
                      height: 24,
                      // color: Colors.white,
                    ),
                  ),
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(
                      Icons.more_vert,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 80),

              // Main Subscription Card
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 24),
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [0.0, 0.3, 1.0],
                    colors: [
                      Color.fromRGBO(255, 255, 255, 0.1),  // Mild white at top
                      Color.fromRGBO(15, 23, 139, 0.15),   // Transition point
                      Color.fromRGBO(3, 11, 121, 0.4),    // Intense dark blue (70%)
                    ],
                  ),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    // Title Section
                    const Text(
                      'Subscription Details',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
              
                    const SizedBox(height: 8),
              
                    const Text(
                      'Select subscription plan',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
              
                    const SizedBox(height: 40),
              
                    // Subscription Plans
                    // 1 Month Plan
                    _buildSubscriptionCard(
                      title: '1 Month Plan',
                      price: '\$9.99',
                      isSelected: false,
                    ),
              
                    const SizedBox(height: 16),
              
                    // 6 Month Plan
                    _buildSubscriptionCard(
                      title: '6 Month Plan',
                      price: '\$12.99',
                      isSelected: true,
                    ),
              
                    const SizedBox(height: 16),
              
                    // 1 Year Plan
                    _buildSubscriptionCard(
                      title: '1 Year Plan',
                      price: '\$15.99',
                      isSelected: false,
                    ),
              
                    // const SizedBox(height: 40),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSubscriptionCard({
    required String title,
    required String price,
    required bool isSelected,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: isSelected 
            ? Colors.white.withValues(alpha: 0.2)
            : const Color.fromARGB(255, 50, 38, 179),
        borderRadius: BorderRadius.circular(10),
        // border: Border.all(
        //   color: isSelected 
        //       ? Colors.white.withValues(alpha: 0.4)
        //       : Colors.white.withValues(alpha: 0.2),
        //   width: isSelected ? 2 : 1,
        // ),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            price,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }


}
