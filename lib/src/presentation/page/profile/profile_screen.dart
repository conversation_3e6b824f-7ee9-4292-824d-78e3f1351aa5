import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../common/images.dart';
import 'subscription_screen.dart';
import 'report_problem_screen.dart';
import 'terms_policies_screen.dart';
import 'help_support_screen.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Images.APP_BG),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header with back button and menu
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Image.asset(
                      'assets/images/back.png',
                      width: 24,
                      height: 24,
                      // color: Colors.white,
                    ),
                  ),
                  IconButton(
                    onPressed: () {},
                    icon: const Icon(
                      Icons.more_vert,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 70),

              // Profile Card with Avatar Overlapping
              Stack(
                clipBehavior: Clip.none,
                children: [
                  // Main Profile Card
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 24),
                    padding: const EdgeInsets.fromLTRB(24, 30, 24, 24),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color.fromARGB(113, 143, 100, 252),  // Lite color at top (50% transparent)
                          Color.fromARGB(173, 30, 17, 58),  // Intense color at bottom (50% transparent)
                        ],
                      ),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        // const SizedBox(height: 3),

                    // Profile Info Box with Name (top line behind 25% of avatar)
                    Container(
                      padding: const EdgeInsets.fromLTRB(16, 35, 16, 16),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        // color: Colors.white.withValues(alpha: 0.1),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          // Name with Edit Icon
                          Stack(
                            children: [
                              Container(
                                // color: Colors.amber,
                                width: double.infinity,
                                alignment: Alignment.center,
                                child: const Text(
                                  'Abdullah',
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                              // Positioned(
                              //   top: 0,
                              //   right: 0,
                              //   child: Container(
                              //     width: 28,
                              //     height: 28,
                              //     decoration: BoxDecoration(
                              //       color: Colors.white.withValues(alpha: 0.2),
                              //       shape: BoxShape.circle,
                              //       border: Border.all(
                              //         color: Colors.white.withValues(alpha: 0.3),
                              //         width: 1,
                              //       ),
                              //     ),
                              //     child: const Icon(
                              //       Icons.edit,
                              //       color: Colors.white,
                              //       size: 14,
                              //     ),
                              //   ),
                              // ),
                            ],
                          ),

                          const SizedBox(height: 20),

                          _buildProfileInfo('Email ID', '<EMAIL>'),
                          const SizedBox(height: 12),
                          _buildProfileInfo('Phone Number', '+966 531 769 192'),
                          const SizedBox(height: 12),
                          _buildProfileInfo('Country', 'Saudi Arabia'),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Support And About Section
                    const Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        'Support And About',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildMenuItem(
                      Icons.star_outline,
                      'My Subscription',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SubscriptionScreen(),
                          ),
                        );
                      },
                    ),
                    _buildMenuItem(
                      Icons.help_outline,
                      'Help and support',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const HelpSupportScreen(),
                          ),
                        );
                      },
                    ),
                    _buildMenuItem(
                      Icons.description_outlined,
                      'Terms and Policies',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const TermsPoliciesScreen(),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 20),

                    // Line Separator
                    Container(
                      height: 1,
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Action Section
                    const Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        'Action',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildMenuItem(
                      Icons.report_outlined,
                      'Report a problem',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ReportProblemScreen(),
                          ),
                        );
                      },
                    ),
                    _buildMenuItem(Icons.logout, 'Logout'),

                    const SizedBox(height: 24),
                  ],
                ),
              ),

              // Positioned Avatar (Half Outside, Half Inside)
              Positioned(
                top: -50,
                left: 0,
                right: 0,
                child: Center(
                  child: Stack(
                    children: [
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 3,
                          ),
                        ),
                        child: ClipOval(
                          child: Image.asset(
                            'assets/images/avatar_1.png',
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                decoration: const BoxDecoration(
                                  color: Colors.white24,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.person,
                                  size: 50,
                                  color: Colors.white70,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
               Positioned(
                                top: 40,
                                right: 60,
                                child: Container(
                                  width: 28,
                                  height: 28,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white.withValues(alpha: 0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: const Icon(
                                    Icons.edit,
                                    color: Colors.white,
                                    size: 14,
                                  ),
                                ),
                              ),
            ],
          ),

          const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileInfo(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            '$label :',
            style: TextStyle(
              fontSize: 12,
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMenuItem(IconData icon, String title, {VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 6),
        child: Row(
          children: [
            Icon(
              icon,
              color: Colors.white.withValues(alpha: 0.8),
              size: 18,
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }


}
