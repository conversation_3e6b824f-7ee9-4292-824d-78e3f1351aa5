import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:sign_language/src/common/constant.dart';
import 'package:sign_language/src/common/images.dart';
import 'package:sign_language/src/common/routes.dart';
import 'package:sign_language/src/presentation/bloc/bloc/language_bloc.dart';
import 'package:sign_language/src/presentation/bloc/bloc/language_event.dart';
import 'package:sign_language/src/presentation/bloc/bloc/language_state.dart';
import 'package:sign_language/src/presentation/widget/custom_gradient_button.dart';
import 'package:sign_language/src/presentation/widget/custom_transparent_button.dart';
import 'package:sign_language/src/presentation/widget/popup_widget.dart';
import '../signup/signup_email_screen.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          // Background image
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(Images.APP_BG),
                fit: BoxFit.cover,
              ),
            ),
          ),

          // Foreground content with scroll
          SafeArea(
            child: SingleChildScrollView(
              reverse: true,
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom + 24,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 75),
                    Center(
                      child: Image.asset(
                        Images.APP_ICON,
                        width: 80,
                        height: 75,
                      ),
                    ),
                    const SizedBox(height: SPACE25),
                    Text(
                      'login'.tr(),
                      style: Theme.of(context).textTheme.displayLarge,
                    ),
                    const SizedBox(height: SPACE15),
                    Text(
                      'enter_email'.tr(),
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    const SizedBox(height: SPACE12),
                    _buildTextField(),
                    const SizedBox(height: SPACE15),
                    Text(
                      'enter_password'.tr(),
                      style: Theme.of(context).textTheme.headlineMedium,
                    ),
                    const SizedBox(height: SPACE12),
                    _buildTextField(obscure: true),
                    const SizedBox(height: SPACE25),
                    CustomGradientButton(
                      onPressed: () {
                        // Navigate to onboarding screen
                        context.push(AppRoutes.ONBOARDING_ROUTE_PATH);
                      },
                      label: 'login'.tr(),
                      height: 56,
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                        onPressed: () {
                          context.push(AppRoutes.FORGOT_PASSWORD_ROUTE_PATH);
                        },
                        style: TextButton.styleFrom(
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: Text(
                          'forgot_password'.tr(),
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 13,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ),
                    Center(
                      child: Text(
                        'or_continue_with'.tr(),
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    const SizedBox(height: SPACE12),
                    Row(
                      children: [
                        Expanded(
                          child: CustomTransparentButton(
                            onTap: () {},
                            icon: Images.IC_Google,
                          ),
                        ),
                        const SizedBox(width: SPACE12),
                        Expanded(
                          child: CustomTransparentButton(
                            onTap: () {},
                            icon: Images.IC_Apple,
                          ),
                        )
                      ],
                    ),
                    const SizedBox(height: SPACE12),
                    Center(
                      child: CustomTransparentButton(
                        onTap: () {},
                        label: 'continue_as_guest'.tr(),
                        isWithIcon: false,
                      ),
                    ),
                    const SizedBox(height: SPACE15),
                    Center(
                      child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          text: '${'dont_have_account'.tr()}\n',
                          style: Theme.of(context).textTheme.headlineMedium,
                          children: [
                            TextSpan(
                              text: 'create_new_account'.tr(),
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineMedium
                                  ?.copyWith(
                                    decoration: TextDecoration.underline,
                                  ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => const SignupEmailScreen(),
                                    ),
                                  );
                                },
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            right: 0,
            top: 50,
            child: Row(
              children: [
                BlocBuilder<LanguageBloc, LanguageState>(
                  builder: (context, state) {
                    context.setLocale(state.locale); // Updates EasyLocalization
                    return TextButton(
                      onPressed: () {
                        context.read<LanguageBloc>().add(ToggleLanguage());
                      },
                      child: Text(
                          state.locale.languageCode.toUpperCase()), // EN / AR
                    );
                  },
                ),
                ProfilePopupButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({bool obscure = false}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextField(
        obscureText: obscure,
        textDirection: ui.TextDirection.ltr,
        textAlign: TextAlign.left,
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
      ),
    );
  }
}
