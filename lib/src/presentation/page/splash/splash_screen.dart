import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sign_language/src/common/colors.dart';
import 'package:sign_language/src/common/constant.dart';
import 'package:sign_language/src/common/images.dart';
import 'package:sign_language/src/common/routes.dart';
import 'package:sign_language/src/presentation/widget/custom_elevated_button.dart';
import 'package:sign_language/src/presentation/widget/custom_gradient_button.dart';
import 'package:sign_language/src/presentation/widget/custom_outlined_button.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    Future.delayed(const Duration(seconds: 1), () {
      context.go(AppRoutes.LOGIN_ROUTE_PATH);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
              image: AssetImage(Images.APP_BG), fit: BoxFit.cover),
        ),
        child: Center(
          child: Image.asset(
            Images.APP_ICON,
            width: 228,
            height: 212,
          ),
        ),
        // color: Colors.black,
      ),
    );
  }
}
