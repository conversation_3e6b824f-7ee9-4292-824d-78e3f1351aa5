import 'dart:ui';

class ColorLight {
  static const Color primary = Color(0xFF102c57);
  static const Color background = Color(0xFFFAFAFA);
  static const Color card = Color(0xFFFFFFFF);
  static const Color fontTitle = Color(0xFF202020);
  static const Color fontSubtitle = Color(0xFF737373);
  static const Color fontDisable = Color(0xFF9B9B9B);
  static const Color disabledButton = Color(0xFFB9B9B9);
  static const Color divider = Color(0xFFDCDCDC);

  static const Color success = Color(0xFF81C784);
  static const Color warning = Color(0xFFFFB74D);
  static const Color error = Color(0xFFE57373);
  static const Color info = Color(0xFF64B5F6);

  // Other
  static const Color silverTree = Color(0xFF5FB4A5);
  static const Color catSkillWhite = Color(0xFFF0F5F9);
  static const Color halfDutchWhite = Color(0xFFFFF6E1);
  static const Color whiteSmoke = Color(0xFFF3F4F8);
  static const Color linkWater = Color(0xFFDAE7F1);
  static const Color purpleMimosa = Color(0xFF9572FF);
  static const Color lightRoyalBlue = Color(0xFF3328EE);
}

class ColorDark {
  static const Color primary = Color(0xFF149CFC);
  static const Color background = Color(0xFF303030);
  static const Color card = Color(0xFF424242);
  static const Color fontTitle = Color(0xFFFFFFFF);
  static const Color fontSubtitle = Color(0xFFC1C1C1);
  static const Color fontDisable = Color(0xFF989898);
  static const Color disabledButton = Color(0xFF6E6E6E);
  static const Color divider = Color(0xFF494949);

  static const Color success = Color.fromARGB(255, 98, 249, 106);
  static const Color warning = Color(0xFFF57C00);
  static const Color error = Color(0xFFD32F2F);
  static const Color info = Color(0xFF1976D2);
  static const Color dark = Color(0xFF000000);
}
