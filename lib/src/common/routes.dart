// ignore_for_file: constant_identifier_names

class AppRoutes {
  // Splash routes
  static const String SPLASH_ROUTE_NAME = 'splash';
  static const String SPLASH_ROUTE_PATH = '/';

  // Authentications routes
  static const String ONBOARDING_ROUTE_NAME = 'onboarding';
  static const String ONBOARDING_ROUTE_PATH = '/onboarding';

  static const String LOGIN_ROUTE_NAME = 'login';
  static const String LOGIN_ROUTE_PATH = '/login';

  static const String SIGNUP_ROUTE_NAME = 'signup';
  static const String SIGNUP_ROUTE_PATH = '/signup';

  static const String FORGOT_PASSWORD_ROUTE_NAME = 'forgot_password';
  static const String FORGOT_PASSWORD_ROUTE_PATH = '/forgot_password';

  static const String EMAIL_CHECK_ROUTE_NAME = 'email_check';
  static const String EMAIL_CHECK_ROUTE_PATH = '/email_check';

  static const String VERIFICATION_ROUTE_NAME = 'verification';
  static const String VERIFICATION_ROUTE_PATH = '/verification';

  static const String CHANGE_PASSWORD_ROUTE_NAME = 'change_password';
  static const String CHANGE_PASSWORD_ROUTE_PATH = '/change_password';

  static const String TRANSLATE_ROUTE_NAME = 'translate';
  static const String TRANSLATE_ROUTE_PATH = '/translate';

  static const String SELECT_REGION_ROUTE_NAME = 'select_region';
  static const String SELECT_REGION_ROUTE_PATH = '/select_region';

  static const String CREATE_PROFILE_ROUTE_NAME = 'create_profile';
  static const String CREATE_PROFILE_ROUTE_PATH = '/create_profile';

  static const String AVATAR_SELECTION_ROUTE_NAME = 'avatar_selection';
  static const String AVATAR_SELECTION_ROUTE_PATH = '/avatar_selection';

  static const String PROFILE_ROUTE_NAME = 'profile';
  static const String PROFILE_ROUTE_PATH = '/profile';

  // Dashboard routes
  static const String DASHBOARD_ROUTE_NAME = 'dashboard';
  static const String DASHBOARD_ROUTE_PATH = '/dashboard';

  // create task routes
  static const String TASK_TITLE_ROUTE_NAME = 'task_title';
  static const String TASK_TITLE_ROUTE_PATH = '/task_title';

  static const String TASK_DATE_ROUTE_NAME = 'task_date';
  static const String TASK_DATE_ROUTE_PATH = '/task_date';

  static const String TASK_LOCATION_ROUTE_NAME = 'task_location';
  static const String TASK_LOCATION_ROUTE_PATH = '/task_location';

  static const String TASK_DETAILS_ROUTE_NAME = 'task_details';
  static const String TASK_DETAILS_ROUTE_PATH = '/task_details';

  static const String TASK_IMAGES_ROUTE_NAME = 'task_images';
  static const String TASK_IMAGES_ROUTE_PATH = '/task_images';

  static const String TASK_BUDGET_ROUTE_NAME = 'task_budget';
  static const String TASK_BUDGET_ROUTE_PATH = '/task_budget';

  static const String PUBLIC_PROFILE_VIEW_SCREEN_NAME =
      'public_prfoile_view_screen';
  static const String PUBLIC_PROFILE_VIEW_SCREEN_PATH =
      '/public_prfoile_view_screen';
}
