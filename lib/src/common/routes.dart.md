## AppRoutes Documentation

This document provides documentation for the `AppRoutes` class, outlining the various routes defined within the application.

### Table of Contents

| Section | Description |
|---|---|
| [Introduction](#introduction) | Overview of `AppRoutes` class. |
| [Routes](#routes) | List of defined routes and their corresponding paths. | 

### Introduction 

The `AppRoutes` class is responsible for defining and managing all routes used within the application. This class provides static constants for route names and paths, simplifying navigation and route management.

### Routes

The following table summarizes the defined routes, their names, and paths.

| Route Name | Route Path | Description |
|---|---|---|
| `SPLASH_ROUTE_NAME` | `/` | Splash screen route. |
| `ONBOARDING_ROUTE_NAME` | `/onboarding` | Onboarding route. |
| `LOGIN_ROUTE_NAME` | `/login` | Login route. |
| `SIGNUP_ROUTE_NAME` | `/signup` | Signup route. |
| `SELECT_REGION_ROUTE_NAME` | `/select_region` | Route for selecting a region. |
| `CREATE_PROFILE_ROUTE_NAME` | `/create_profile` | Route for creating a user profile. |
| `DASHBOARD_ROUTE_NAME` | `/dashboard` | Dashboard route. |
| `TASK_TITLE_ROUTE_NAME` | `/task_title` | Route for entering a task title. |
| `TASK_DATE_ROUTE_NAME` | `/task_date` | Route for selecting a task date. |
| `TASK_LOCATION_ROUTE_NAME` | `/task_location` | Route for selecting a task location. |
| `TASK_DETAILS_ROUTE_NAME` | `/task_details` | Route for providing task details. |
| `TASK_IMAGES_ROUTE_NAME` | `/task_images` | Route for uploading task images. |
| `TASK_BUDGET_ROUTE_NAME` | `/task_budget` | Route for setting a task budget. |
| `PUBLIC_PROFILE_VIEW_SCREEN_NAME` | `/public_prfoile_view_screen` | Route for viewing a public profile. | 
