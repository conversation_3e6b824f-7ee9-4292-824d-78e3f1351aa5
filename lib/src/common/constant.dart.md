## Internal Code Documentation - Constants

### Table of Contents 
- [String Constants](#string-constants)
- [Double Constants](#double-constants)

### String Constants

| Constant | Value | Description | 
|---|---|---|
| `ACCESS_TOKEN` | `'access_token'` | Represents the key for storing the access token. |
| `ONBOARDING` | `'onboarding'` | Represents the key for storing onboarding data. |

### Double Constants

| Constant | Value | Description | 
|---|---|---|
| `MARGIN` | `18` | Represents the default margin value. |
| `RADIUS` | `8` | Represents the default radius value. |
| `SPACE8` | `8` | Represents a spacing value of 8. |
| `SPACE4` | `4` | Represents a spacing value of 4. |
| `SPACE12` | `12` | Represents a spacing value of 12. |
| `SPACE15` | `15` | Represents a spacing value of 15. |
| `SPACE25` | `25` | Represents a spacing value of 25. | 
