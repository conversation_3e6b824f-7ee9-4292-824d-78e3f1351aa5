import 'dart:async';

import 'package:bloc_concurrency/bloc_concurrency.dart' as bloc_concurrency;
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sign_language/src/common/themes.dart';
import 'package:sign_language/src/presentation/bloc/bloc/language_bloc.dart';
import 'package:sign_language/src/presentation/cubit/theme/theme_cubit.dart';
import 'package:sign_language/src/utilities/app_bloc_observer.dart';
import 'package:sign_language/src/utilities/go_router_init.dart';
import 'package:sign_language/src/utilities/logger.dart';
import 'package:sign_language/injection.dart' as di;

Future<void> main() async {
  logger.runLogging(
    () => runZonedGuarded(
      () async {
        WidgetsFlutterBinding.ensureInitialized();
        await EasyLocalization.ensureInitialized();
        Bloc.transformer = bloc_concurrency.sequential();
        Bloc.observer = const AppBlocObserver();
        di.init();

        runApp(
          EasyLocalization(
            supportedLocales: const [Locale('en'), Locale('ar')],
            path: 'assets/lang',
            fallbackLocale: const Locale('en'),
            child: const MyApp(),
          ),
        );
      },
      logger.logZoneError,
    ),
    const LogOptions(),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => di.locator<ThemeCubit>()),
        BlocProvider<LanguageBloc>(create: (_) => LanguageBloc()),
      ],
      child: MaterialApp.router(
        localizationsDelegates: context.localizationDelegates,
        supportedLocales: context.supportedLocales,
        locale: context.locale,
        debugShowCheckedModeBanner: false,
        title: 'flutter bloc clean architecture',
        theme: themeLight(context),
        darkTheme: themeDark(context),
        themeMode: ThemeMode.dark,
        routerConfig: routerinit,
      ),
    );
  }
}
