import 'package:get_it/get_it.dart';
import 'package:sign_language/src/data/datasource/authentication_remote_data_source.dart';
import 'package:sign_language/src/data/repository/authentication_repository_impl.dart';
import 'package:sign_language/src/domain/repositories/autentication_repository.dart';
import 'package:sign_language/src/domain/usecase/login.dart';
import 'package:sign_language/src/presentation/cubit/theme/theme_cubit.dart';

final locator = GetIt.instance;

void init() {
  //TODO: Data sources
  final authRemoteDataSource = AuthenticationRemoteDataSourceImpl();
  locator.registerLazySingleton<AuthenticationRemoteDataSource>(
    () => authRemoteDataSource,
  );

  // Repositories
  final authRepository = AuthenticationRepositoryImpl(locator());
  locator.registerLazySingleton<AuthenticationRepository>(
    () => authRepository,
  );

  // Use cases
  final signIn = SignIn(locator());
  locator.registerLazySingleton(
    () => signIn,
  );

  //TODO:  BLoCs

  final themeCubit = ThemeCubit();
  locator.registerLazySingleton(
    () => themeCubit,
  );
}
