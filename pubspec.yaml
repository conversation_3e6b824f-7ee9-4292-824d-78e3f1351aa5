name: sign_language
description: "A new Flutter project."
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.4.4 <4.0.0'

dependencies:
  bloc_concurrency: ^0.3.0
  carousel_slider: ^5.0.0
  country_code_picker: ^3.3.0
  cupertino_icons: ^1.0.6
  dartz: ^0.10.1
  dio: ^5.8.0+1
  easy_localization: ^3.0.7+1
  equatable: ^2.0.7
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.1
  flutter_spinkit: ^5.2.1
  fluttertoast: ^8.2.12
  font_awesome_flutter: ^10.8.0
  form_field_validator: ^1.1.0
  freezed_annotation: ^2.4.3
  get_it: ^8.0.3
  go_router: ^15.1.2
  google_fonts: ^6.2.1
  introduction_screen: ^3.1.17
  logger: ^2.5.0
  shared_preferences: ^2.3.3

dev_dependencies:
  build_runner: ^2.4.10
  flutter_lints: ^3.0.0
  flutter_test:
    sdk: flutter
  freezed: ^2.5.2
  very_good_analysis: ^5.1.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/lang/
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
